/// Example usage of GuidedPathSeederFromJson
/// 
/// This example demonstrates how to use the new JSON-based guided path seeder
/// as a drop-in replacement for the original hardcoded seeder.
library;

import 'package:flutter/material.dart';
import 'package:upshift/utils/admin/guided_path_seeder_from_json.dart';
import 'package:upshift/utils/admin/guided_path_seeder.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Guided Path Seeder Example',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const SeederComparisonPage(),
    );
  }
}

class SeederComparisonPage extends StatefulWidget {
  const SeederComparisonPage({super.key});

  @override
  State<SeederComparisonPage> createState() => _SeederComparisonPageState();
}

class _SeederComparisonPageState extends State<SeederComparisonPage> {
  String _status = 'Ready to compare seeders';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Guided Path Seeder Comparison'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Compare Original vs JSON-based Seeder',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _compareDataStructure,
              child: const Text('Compare Data Structure'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _validateJsonSeeder,
              child: const Text('Validate JSON Seeder'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testCompatibility,
              child: const Text('Test API Compatibility'),
            ),
            const SizedBox(height: 20),
            
            if (_isLoading)
              const Center(child: CircularProgressIndicator()),
            
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _status,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _compareDataStructure() async {
    setState(() {
      _isLoading = true;
      _status = 'Comparing data structures...\n';
    });

    try {
      // Get data from original seeder
      final originalData = GuidedPathSeeder.getDefaultPathsWithSteps();
      
      // Get data from JSON seeder
      final jsonData = await GuidedPathSeederFromJson.getDefaultPathsWithSteps();
      
      _appendStatus('✅ Original seeder: ${originalData.length} paths');
      _appendStatus('✅ JSON seeder: ${jsonData.length} paths');
      
      if (originalData.length == jsonData.length) {
        _appendStatus('✅ Path count matches!');
      } else {
        _appendStatus('❌ Path count mismatch!');
      }
      
      // Compare categories
      final originalCategories = GuidedPathSeeder.getDefaultCategories();
      final jsonCategories = await GuidedPathSeederFromJson.getDefaultCategories();
      
      _appendStatus('\nCategories comparison:');
      _appendStatus('Original: ${originalCategories.join(', ')}');
      _appendStatus('JSON: ${jsonCategories.join(', ')}');
      
      // Compare starter paths
      final originalStarter = GuidedPathSeeder.getStarterPathName();
      final jsonStarter = await GuidedPathSeederFromJson.getStarterPathName();
      
      _appendStatus('\nStarter path comparison:');
      _appendStatus('Original: $originalStarter');
      _appendStatus('JSON: $jsonStarter');
      
      if (originalStarter == jsonStarter) {
        _appendStatus('✅ Starter paths match!');
      } else {
        _appendStatus('❌ Starter paths differ!');
      }
      
    } catch (e) {
      _appendStatus('❌ Error during comparison: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _validateJsonSeeder() async {
    setState(() {
      _isLoading = true;
      _status = 'Validating JSON seeder...\n';
    });

    try {
      // Test JSON structure validation
      final isValid = await GuidedPathSeederFromJson.validateJsonStructure();
      _appendStatus('✅ JSON structure validation: $isValid');
      
      // Test data loading
      final pathsWithSteps = await GuidedPathSeederFromJson.getDefaultPathsWithSteps();
      _appendStatus('✅ Successfully loaded ${pathsWithSteps.length} paths');
      
      // Validate each path
      for (final pathData in pathsWithSteps) {
        final guidedPath = pathData['path'];
        final steps = pathData['steps'];
        _appendStatus('  - ${guidedPath.name}: ${steps.length} steps');
      }
      
    } catch (e) {
      _appendStatus('❌ Validation error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testCompatibility() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing API compatibility...\n';
    });

    try {
      // Test that both seeders have the same public interface
      _appendStatus('Testing method compatibility:');
      
      // Test getDefaultPathsWithSteps
      final originalPaths = GuidedPathSeeder.getDefaultPathsWithSteps();
      final jsonPaths = await GuidedPathSeederFromJson.getDefaultPathsWithSteps();
      _appendStatus('✅ getDefaultPathsWithSteps() - both return data');
      
      // Test getDefaultCategories
      final originalCategories = GuidedPathSeeder.getDefaultCategories();
      final jsonCategories = await GuidedPathSeederFromJson.getDefaultCategories();
      _appendStatus('✅ getDefaultCategories() - both return categories');
      
      // Test getStarterPathName
      final originalStarter = GuidedPathSeeder.getStarterPathName();
      final jsonStarter = await GuidedPathSeederFromJson.getStarterPathName();
      _appendStatus('✅ getStarterPathName() - both return starter path');
      
      // Note: pathsExist, seedIfEmpty, and seedDefaultPathsWithSteps require Firestore
      _appendStatus('\n⚠️  Firestore-dependent methods not tested (require database connection)');
      _appendStatus('   - pathsExist()');
      _appendStatus('   - seedIfEmpty()');
      _appendStatus('   - seedDefaultPathsWithSteps()');
      
      _appendStatus('\n✅ API compatibility test completed successfully!');
      _appendStatus('The JSON-based seeder can be used as a drop-in replacement.');
      
    } catch (e) {
      _appendStatus('❌ Compatibility test error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _appendStatus(String message) {
    setState(() {
      _status += '$message\n';
    });
  }
}
